import xbot_visual
from .logger import logger
from .package import variables as glv

def notify(message, withAt=False):
  webhook = glv.get("notify_webhook")
  
  if withAt:
      notify_users = glv.get("notify_users", [])
      for user in notify_users:
          user_id = user.get("user_id")
          user_name = user.get("user_name")
          message = f"<at user_id=\"{user_id}\">{user_name}</at>{message}"
  xbot_visual.web_service.send_feishu_msg(
      webhook=webhook,
      secret=None,
      msgtype="text",
      text_content=f"{message}",
      app_id="",
      app_secret="",
      image_file_path="",
      post_content="",
      interactive_content="",
      _block=("error", 5, "飞书群通知")
)

def notify_with_lark_and_email(title, content):
    notify_list = glv.get("notify_users", ["<EMAIL>"])
    result = xbot_visual.process.run(process="xbot_extensions.activity_a771ba63.main", package=__name__, inputs={
        "收件人": lambda: notify_list,
        "标题": title,
        "内容": content,
        "附件": "",
        "发送类型": lambda: ["feishuMsg", "emailMsg"],
        "飞书机器人编码": "default",
        "popo机器人账号": "<EMAIL>",
        }, outputs=[
        "result",
    ], _block=("main", 3, "[通用]企业内部消息通知"))

def notify_with_lark(content, notify_list = None):
    notify_list = notify_list or glv.get("notify_users", [])
    result = xbot_visual.process.run(process="xbot_extensions.activity_a771ba63.main", package=__name__, inputs={
        "收件人": lambda: notify_list,
        "标题": "",
        "内容": content,
        "附件": "",
        "发送类型": lambda: ["feishuMsg"],
        "飞书机器人编码": "default",
        "popo机器人账号": "<EMAIL>",
        }, outputs=[
        "result",
    ], _block=("main", 3, "[通用]企业内部消息通知"))

def notify_with_email(title, content, notify_list = None):
    notify_list = notify_list or glv.get("notify_users", [])
    result = xbot_visual.process.run(process="xbot_extensions.activity_a771ba63.main", package=__name__, inputs={
        "收件人": lambda: notify_list,
        "标题": title,
        "内容": content,
        "附件": "",
        "发送类型": lambda: ["emailMsg"],
        "飞书机器人编码": "default",
        "popo机器人账号": "<EMAIL>",
    }, outputs=[
        "result",
    ], _block=("main", 3, "[通用]企业内部消息通知"))
