import os
import xbot
import datetime
import asyncio
import time
import json

from xbot import print, sleep, web, win32
from .import package
from .package import variables as glv
from .logger import logger
from .utils import safe_find, safe_find_all, is_exist
from .dy_login import login
from .notify import notify, notify_with_email, notify_with_lark
from .download import download
from .mark import mark
from .lark import delete_rows, get_lark_sheet, get_row_num, delete_rows, update_data_by_range, set_cell_format, get_data_by_range

folder = "C:\\short_video\\douyin\\raw"
folder_token = "BS1jfPfZ1l2X8zdamHJcCP8Pneg"

# 罗盘经营数据下载
def startLuopan(shop_list, target_date, notify_list, need_notify = True):
    # 创建文件夹
    if not os.path.exists(folder):
        os.makedirs(folder)

    yesterday = (datetime.date.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    if not target_date:
        target_date = yesterday

    lark_sheet_title = f"{target_date}爆款短视频监控数据"
    lark_sheet = get_lark_sheet(folder_token, lark_sheet_title)
    # 判断表格中是否已经有数据，有数据的话则清空数据
    row_num = get_row_num(lark_sheet["sheet_instance"])
    if row_num > 1:
        delete_rows(lark_sheet["sheet_instance"], 2, row_num - 1)
    update_data_by_range(lark_sheet["sheet_instance"], "A1", [["视频仅供阅读，版权归创作者所有，网易严选无使用权，未经沟通的使用需自行承担法律责任以及赔款。如需采购联系@刘雨欣"]])
    update_data_by_range(lark_sheet["sheet_instance"], "A2", [["爆款视频来源类型", "标签", "具体来源", "视频标题", "视频链接", "视频ID", "视频发布时间", "达人信息", "带货商品ID", "商品名称", "视频观看次数", "成交金额(元)", "销量"]])

    for shop in shop_list:
        # 下载店铺数据
        download(shop, yesterday, target_date, folder)

        # 店铺数据打标&发送
        # mark(shop, target_date, folder, lark_sheet["sheet_instance"])

    # 执行完成后，统一通知
    if need_notify:
        dt = datetime.datetime.strptime(target_date, "%Y-%m-%d")
        target_date_zh = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日"
        notify_title = f"{target_date_zh} 爆款短视频监控通知"

        result_row_num = get_row_num(lark_sheet["sheet_instance"])
        # 没有数据
        if result_row_num > 1:
            # 给标签为大爆的第二列设置文字颜色
            set_color(lark_sheet["sheet_instance"])

            notify_content_lark = f"以下是{target_date_zh}识别到的爆款短视频信息，请点击查看\n{lark_sheet['sheet_url']}"
            notify_content_email = f"以下是{target_date_zh}识别到的爆款短视频信息，请点击<a href=\"{lark_sheet['sheet_url']}\">查看</a>"
            notify_with_email(notify_title, notify_content_email, notify_list)
            notify_with_lark(notify_content_lark, notify_list)
    pass
  
# 飞瓜竞品数据下载
def startFeigua(brand_list, target_date):
    # 创建文件夹
    if not os.path.exists(folder):
        os.makedirs(folder)

    yesterday = (datetime.date.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    if not target_date:
        target_date = yesterday

    for shop in brand_list:
        # 下载店铺数据
        download(shop, yesterday, target_date, folder)
    pass

# 设置颜色
def set_color(feishu_instance):
    # 读取所有数据
    row_num = get_row_num(feishu_instance)
    data = get_data_by_range(feishu_instance, "B1:B" + str(row_num))
    # 遍历数据
    for i in range(row_num):
        if data[i][0] == "大爆":
            set_cell_format(feishu_instance, "B" + str(i + 1), "#F54A45")

def main(args):
    shop_list = [
        {
            "channel_name": "网易严选洗护清洁旗舰店",
            "channel_id": "6901857",
        },
        {
            "channel_name": "网易严选官方旗舰店",
            "channel_id": "5995821",
        },
    ]
    yesterday = (datetime.date.today() + datetime.timedelta(days=-1)).strftime("%Y-%m-%d")
    target_date = ''
    if not target_date:
        target_date = yesterday
    notify_list = ["<EMAIL>"]
    startLuopan(shop_list, target_date, notify_list)
    # for shop in shop_list:
    #     download(shop, yesterday, target_date, folder)
