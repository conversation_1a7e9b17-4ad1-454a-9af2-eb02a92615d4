from .logger import logger

def safe_find(yd_win, seletor, timeout=1):
  try:
    return yd_win.find(seletor, timeout=timeout)
  except Exception as e:
    logger.info(e)
    return None

def safe_find_all(yd_win, seletor, timeout=1):
  try:
    return yd_win.find_all(seletor, timeout=timeout)
  except Exception as e:
    logger.info(e)
    return []

# 判断元素是否存在
def is_exist(yd_win, selector, timeout=0.1):
  try:
    return yd_win.wait_appear(selector, timeout=timeout)
  except Exception as e:
    logger.info(e)
    return False

# 获取合作短视频文件名
def get_cooperation_video_file_name(channel_name, target_date):
  return f"{channel_name}_合作_{target_date}.xlsx"

# 获取自营短视频文件名
def get_self_video_file_name(channel_name, target_date):
  return f"{channel_name}_自营_{target_date}.xlsx"

# 获取飞瓜竞品数据文件名
def get_feigua_competitor_file_name(brand_name, target_date):
  return f"{brand_name}_竞品_{target_date}.xlsx"