# 数据打标
from math import log
import os
import xbot
import datetime
import asyncio
import time
import json
import pandas as pd


from xbot import print, sleep, web, win32, app
from .import package
from .package import variables as glv
from .logger import logger
from .utils import safe_find, safe_find_all, is_exist, get_self_video_file_name, get_cooperation_video_file_name
from .dy_login import login
from .notify import notify, notify_with_lark_and_email, notify_with_email, notify_with_lark
from .lark import create_spreadsheet, get_file_list, get_instance, update_data_by_range, get_lark_sheet, get_row_num, append_data
from .api import get_item_name

# 数据打标，返回lark_sheet_url
def mark(shop, target_date, folder, lark_sheet_instance):
    self_video_file_name = os.path.join(folder, get_self_video_file_name(shop['channel_name'], target_date))
    cooperation_video_file_name = os.path.join(folder, get_cooperation_video_file_name(shop['channel_name'], target_date))
    
    # 处理数据
    self_df = process_data(self_video_file_name, shop, "抖音-自营")
    logger.info(f"店铺：{shop['channel_name']} 自营打标后数据量：{len(self_df)}")
    cooperation_df = process_data(cooperation_video_file_name, shop, "抖音-达人")
    logger.info(f"店铺：{shop['channel_name']} 达人打标后数据量：{len(cooperation_df)}")

    # 合并数据
    df = pd.concat([self_df, cooperation_df], ignore_index=True)

    logger.info(f"店铺：{shop['channel_name']} 打标数据量：{len(df)}")
    
    if not df.empty:
        # 追加数据
        append_data(lark_sheet_instance, df.values.tolist())


# 处理数据
def process_data(file_path, shop, source_type):
    # 1. 读取excel文件
    df = read_excel(file_path)
    if df is None or df.empty:
        return pd.DataFrame()

    logger.info(f"店铺：{shop['channel_name']}-{source_type} 原始数据量：{len(df)}")

    # 2. 筛选出成交金额大于1w到3w的
    df_hot = df[(df["成交金额(元)"] >= 10000) & (df["成交金额(元)"] < 30000)].copy()
    # 标签列标记为爆款
    df_hot["标签"] = "爆款"
    # 3. 筛选出成交金额大于3w的
    df_big = df[df["成交金额(元)"] >= 30000].copy()
    # 标签列标记为大爆
    df_big["标签"] = "大爆"

    # 合并爆款和大爆
    df = pd.concat([df_big, df_hot], ignore_index=True)

    # 4. 爆款视频来源类型
    df["爆款视频来源类型"] = source_type
    # 5. 具体来源取值为 店铺的channel_name
    df["具体来源"] = shop['channel_name']
    df["视频链接"] = df["播放链接"]
    df["视频发布时间"] = df["发布时间"]
    # 10. 达人信息取数据中的 达人昵称-达人抖音号
    df["达人信息"] = df["达人昵称"] + "-" + df["达人抖音号"]
    # 12. 商品名称需要通过一个接口根据商品ID获取
    df["商品名称"] = df["带货商品ID"].apply(lambda x: get_product_name(shop['channel_id'], x))
    # 15. 销量取数据中的成交订单数
    df["销量"] = df["成交订单数"]
    # 返回需要的字段
    return df[["爆款视频来源类型", "标签", "具体来源", "视频标题", "视频链接", "视频ID", "视频发布时间", "达人信息", "带货商品ID", "商品名称", "视频观看次数", "成交金额(元)", "销量"]]

# 获取商品名称
def get_product_name(channel_id, product_id):
    """
    根据商品ID获取商品名称
    """
    try:
        resp = get_item_name(channel_id, product_id)
        if resp and resp.get("data"):
            return resp.get("data")[0].get("out_item_name")
        return None
    except Exception as e:
        logger.error(f"获取商品名称失败：{e}")
        return None
    
# 读取excel
def read_excel(file_path):
    """
    读取excel文件
    """
    try:
        # 指定ID列为字符串类型，防止读取时被转换为科学计数法
        df = pd.read_excel(file_path, dtype={'带货商品ID': str, '视频ID': str, '视频观看次数': str, '成交金额（元）': float, '成交订单数': str})
        return df
    except Exception as e:
        logger.error(f"读取excel文件失败：{e}")
        return None

def main(args):
    shop = {
        "channel_name": "网易严选洗护清洁旗舰店",
        "channel_id": "6901857",
    }
    folder = "C:\\short_video\\douyin\\raw"
    target_date = '2025-07-07'
    folder_token = "BS1jfPfZ1l2X8zdamHJcCP8Pneg"

    #   mark(shop, target_date, folder)
    lark_sheet_title = f"{target_date}爆款短视频监控数据"
    # # logger.info(f"查询飞书目录是否已经存在文件：{is_exist_file(folder_token, lark_sheet_title)}")  
    lark_sheet = get_lark_sheet(folder_token, lark_sheet_title)
    #   if sheet_instance:
    #     update_data_by_range(sheet_instance, "A1", [["爆款视频来源类型", "具体来源", "视频标题", "视频链接", "视频ID", "视频发布时间", "达人信息", "带货商品ID", "商品名称", "视频观看次数", "成交金额(元)", "销量"]])
    dt = datetime.datetime.strptime(target_date, "%Y-%m-%d")
    target_date_zh = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日"
    notify_title = f"{target_date_zh} 爆款短视频监控通知"
    notify_content = f"以下是{target_date_zh}识别到的爆款短视频信息，请点击<a href=\"{lark_sheet['sheet_url']}\">查看</a>"
    notify_with_lark_and_email(title=notify_title, content=notify_content)
