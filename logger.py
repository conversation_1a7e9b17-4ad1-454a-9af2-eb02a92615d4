import os
import logging
import time
import xbot

class Logger(object):
    """
    日志类，写入日志文件，并提供debug、info、warning、error等方法
    日志文件保存在当前目录下的log文件夹中，日志文件名为app-2023-01-01.log
    日志文件会保留7天，超过7天的日志文件会被删除
    """
    def __init__(self, logger_path="/logs", remain_days=7, Flevel=logging.DEBUG):
        path = os.getcwd() + logger_path
        if not os.path.exists(path):
            os.makedirs(path)
        name = "app"
        
        # Remove log files older than remain_days days
        current_time = time.time()
        for file in os.listdir(path):
            file_path = os.path.join(path, file)
            if os.path.isfile(file_path):
                file_time = os.path.getmtime(file_path)
                if current_time - file_time > remain_days * 24 * 60 * 60:
                    os.remove(file_path)
                
        self.time = time.strftime("%Y-%m-%d")
        self.fileName = path + name + "-" + self.time + ".log"
        xbot.logging.info(self.fileName)
        self.logger = logging.getLogger(self.fileName)
        self.logger.setLevel(Flevel) 
        fmt = logging.Formatter('[%(asctime)s] [%(levelname)s]：%(message)s')
        fh = logging.FileHandler(self.fileName, encoding='utf-8')
        fh.setFormatter(fmt)
        fh.setLevel(Flevel)
        self.logger.addHandler(fh)
        fh.close()

    
    def debug(self, message, xbot_logging=True):
        self.logger.debug(message)
        xbot_logging and xbot.logging.debug(message)

    def info(self, message, xbot_logging=True):
        self.logger.info(message)
        xbot_logging and xbot.logging.info(message)
    
    def warning(self, message, xbot_logging=True):
        self.logger.warning(message)
        xbot_logging and xbot.logging.warning(message)

    def error(self, message, xbot_logging=True):
        self.logger.error(message)
        xbot_logging and xbot.logging.error(message)

logger = Logger(logger_path="/logs/aftersale/", remain_days=30)

def main(args):
    logger.debug('一个debug信息')
    logger.info('一个info信息')
    logger.warning('一个warning信息')
    logger.error('一个error信息')

if __name__ == '__main__':
    main(None)