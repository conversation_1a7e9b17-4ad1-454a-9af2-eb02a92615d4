import xbot_visual
from .logger import logger

# 登录
def login(channel_id, target_url):
    code, message, page = xbot_visual.process.run(
        process="xbot_extensions.activity_0062cad9.main",
        package=__name__, inputs={
            "店铺名称": None,
            "渠道id": int(channel_id),
            "网址": "https://fxg.jinritemai.com/ffa/maftersale/aftersale/list",
        },
        outputs=[
            "code",
            "message",
            "page",
        ],
        _block=("main", 1, "【通用】抖店手机登录流程")
    )
    
    url = page.get_url()
    logger.info(f"当前页面地址: {url}, 目标地址: {target_url}")
    if url != target_url:
        page.navigate(target_url)
        # 等待页面加载
        page.wait_load_completed()
    return page
