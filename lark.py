# 飞书表格
import os
import xbot
import datetime
import asyncio
import time
import json
import xbot_visual

from xbot import print, sleep, web, win32
from .import package
from .package import variables as glv
from .logger import logger
from .utils import safe_find, safe_find_all, is_exist
from .dy_login import login
from .notify import notify
from .client import FileClient, CreateSheetClient

# 查询文件夹下的文件清单
def get_file_list(folder_token):
    """
    获取指定文件夹下的文件清单
    """
    _, user_name, password = xbot_visual.asset.get_asset(asset_name="飞书机器人", asset_type="certificate", encrypt_flag="1", asset_info="{\"asset_id\":\"2dc6461a-6956-4872-8b04-ac1f36bedaf8\",\"asset_template\":null}", _block=("main", 4, "获取资产"))
    real_password = xbot_visual.decrypt(password)
    file_client = FileClient(user_name, real_password)
    res = file_client.get_files(folder_token=folder_token, page_size=200)
    return res

# 在特定目录下新建电子表格
def create_spreadsheet(title, folder_token):
    """
    在指定文件夹下新建一个电子表格
    """
    _, user_name, password = xbot_visual.asset.get_asset(asset_name="飞书机器人", asset_type="certificate", encrypt_flag="1", asset_info="{\"asset_id\":\"2dc6461a-6956-4872-8b04-ac1f36bedaf8\",\"asset_template\":null}", _block=("main", 4, "获取资产"))
    real_password = xbot_visual.decrypt(password)
    sheets_client = CreateSheetClient(user_name, real_password)
    res = sheets_client.create_sheet(title, folder_token, )
    return res["spreadsheet"]

# 缓存3分钟
feishu_instance_list = []

def get_instance(spreadsheet):
  global feishu_instance_list
  # 在feishu_instance_list中找到对应的spreadsheet
  feishu_instance = None
  for instance in feishu_instance_list:
    if instance.get("spreadsheet") == spreadsheet:
      # 3分钟内有效
      if time.time() - instance.get("create_time") < 180:
        feishu_instance = instance.get("feishu_instance")
        break
      else:
        feishu_instance_list.remove(instance)

  if feishu_instance:
    return feishu_instance
  
  _, App_id, App_s = xbot_visual.asset.get_asset(asset_name="飞书机器人", asset_type="certificate", encrypt_flag="1", asset_info="{\"asset_id\":\"2dc6461a-6956-4872-8b04-ac1f36bedaf8\",\"asset_template\":null}", _block=("main", 4, "获取资产"))
  feishu_instance = xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process2",
    package=__name__,
    inputs={
      "app_id": App_id,
      "app_secret": xbot_visual.decrypt(App_s),
      "spreadsheet": spreadsheet,
    },
    outputs=[
      "feishu_instance",
    ],
    _block=("main", 10, "建立表格连接")
  )
  feishu_instance_list.append({
    "spreadsheet": spreadsheet,
    "feishu_instance": feishu_instance,
    "create_time": time.time()
  })
  return feishu_instance

def get_row_num(feishu_instance, sheet_name=""):
  row_num = xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process11",
    package=__name__,
    inputs={
      "飞书对象": feishu_instance,
      "读取方式": "ROW",
      "Sheet页名称": sheet_name,
      },
    outputs=[
      "读取结果",
    ], _block=("lark_excel", 12, "读取总行/列数"))
  return row_num

def get_data_by_range(feishu_instance, content_range, sheet_name=""):
  data = xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process1",
    package=__name__,
    inputs={
      "feishu_instance": feishu_instance,
      "dim": "range",
      "valueRenderOption": "ToString",
      "content_range": content_range,
      "sheet_name": sheet_name,
    },
    outputs=[
      "feishu_data",
    ],
    _block=("main", 13, "读取表格数据")
  )
  return data

def get_data_by_cell(feishu_instance, cell, sheet_name=""):
  data = xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process1",
    package=__name__,
    inputs={
      "feishu_instance": feishu_instance,
      "dim": "cell",
      "valueRenderOption": "ToString",
      "content_range": cell,
      "sheet_name": sheet_name,
    },
    outputs=[
      "feishu_data",
    ],
    _block=("main", 13, "读取表格数据")
  )
  return data

def update_data_by_cell(feishu_instance, cell, value, sheet_name="",):
  xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process3",
    package=__name__,
    inputs={
      "feishu_instance": feishu_instance,
      "dim": "cell",
      "content_range": cell,
      "value": value,
      "sheet_name": sheet_name,
    },
    outputs=[],
    _block=("main", 14, "写入表格数据")
  )
  
def update_data_by_range(feishu_instance, range, value, sheet_name=""):
  xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process3",
    package=__name__,
    inputs={
      "feishu_instance": feishu_instance,
      "dim": "range",
      "content_range": range,
      "value": value,
      "sheet_name": sheet_name,
    },
    outputs=[],
    _block=("main", 15, "写入表格数据")
  )

# 在最后一行追加数据
def append_data(feishu_instance, value, sheet_name=""):
  xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process4",
    package=__name__,
    inputs={
      "feishu_instance": feishu_instance,
      "content_range": "",
      "value": lambda: value,
      "sheet_name": sheet_name,
    },
    outputs=[],
    _block=("main", 16, "追加表格数据")
  )
  
# 单元格嵌入图片
def insert_image(feishu_instance, cell, image_path, sheet_name=""):
  xbot_visual.process.run(
    process="xbot_extensions.activity_feishu.process18",
    package=__name__,
    inputs={
      "飞书对象": feishu_instance,
      "嵌入位置": cell,
      "图片路径": image_path,
      "Sheet页名称": sheet_name,
    },
    outputs=[],
    _block=("main", 17, "单元格嵌入图片")
  )

# 查询飞书目录是否已经存在文件
def is_exist_file(folder_token, title):
    """
    查询指定文件夹下是否存在指定标题的文件
    """
    file_list_res = get_file_list(folder_token)
    # 按编辑时间倒序
    for file in file_list_res["files"]:
        if file["name"] == title and file["type"] == "sheet":
            return file
    return None

# 新建飞书表格
def create_lark_sheet(title, folder_token):
    """
    在指定文件夹下新建一个电子表格
    """
    spreadsheet = create_spreadsheet(title, folder_token)
    if spreadsheet:
        return spreadsheet
    return None

# 获取飞书表格实例和表格url
def get_lark_sheet(folder_token, title):
    """
    获取指定电子表格的实例
    """
    exist_file = is_exist_file(folder_token, title)
    if exist_file:
        return {
            "sheet_instance": get_instance(exist_file["token"]),
            "sheet_url": exist_file["url"],
        }
    # 不存在，新建
    new_spreadsheet = create_lark_sheet(title, folder_token)
    if new_spreadsheet:
        return {
            "sheet_instance": get_instance(new_spreadsheet["spreadsheet_token"]),
            "sheet_url": new_spreadsheet["url"],
        }
    return None

# 删除行数据
def delete_rows(feishu_instance, index, count, sheet_name=""):
    xbot_visual.process.run(process="xbot_extensions.activity_feishu.process9", package=__name__, inputs={
        "feishu_instance": feishu_instance,
        "dimension": "ROWS",
        "index": index,
        "count": count,
        "sheet_name": sheet_name,
        }, outputs=[
    ], _block=("main", 6, "删除行列"))

# 删除列数据
def delete_columns(feishu_instance, index, count, sheet_name=""):
    xbot_visual.process.run(process="xbot_extensions.activity_feishu.process9", package=__name__, inputs={
        "feishu_instance": feishu_instance,
        "dimension": "COLUMNS",
        "index": index,
        "count": count,
        "sheet_name": sheet_name,
        }, outputs=[
    ], _block=("main", 6, "删除列数据"))

# 设置单元格文字颜色
def set_cell_format(feishu_instance, cell, color, sheet_name=""):
    xbot_visual.process.run(process="xbot_extensions.activity_feishu.process15", package=__name__, inputs={
      "feishu_instance": feishu_instance,
      "dim": "CELL",
      "set_range": cell,
      "formatter": "NONE",
      "font_size": "",
      "font_bold": False,
      "sheet_name": sheet_name,
      "h_align": "0",
      "v_align": "1",
      "border_type": "NONE",
      "back_color": "",
      "fore_color": color,
      }, outputs=[
  ], _block=("main", 5, "设置格式"))