import xbot_visual
from .logger import logger
from .package import variables as glv
from .notify import notify


base_url = "/base/xhr/rpaOpen/waiter"

def request_api(url, method, params):
  logger.info(f"request_api: {url}, {method}, {params}")
  if method == 'post':
    headers = "accept:application/json\r\nContent-Type:application/json"
  try:
    response = xbot_visual.process.run(
      process="xbot_extensions.activity_c4578137.main",
      package=__name__,
      inputs={
        "request_method": method,
        "request_url": f"{base_url}{url}",
        "headers": headers,
        "request_parameter": lambda: params,
        "env": "online",
      },
      outputs=[
        "response",
      ],
      _block=("main", 2, "通用http接口封装")
    )
    logger.info(response)
    return response
  except Exception as e:
    logger.error(e)
    return None

# 根据商品id查商品名称
def get_item_name(channel_id, item_id):
  params = {
    "page": 1,
    "size": 1000,
    "params": {
      "out_item_id":[item_id],
      "org_channel_id":[channel_id]
    }
  }
  response = request_api("/waiter-query/api/query/632c1fb83450113db4643a9a/yanxuan-rpa-common-server", "post", params)
  if response and response.get("code") == "200":
    return response.get("data", {})
  else:
    return None

def main(args):
  # get_task_list()
  get_item_name("6901857", "3656004817312201122")

if __name__ == "__main__":
  main(None)