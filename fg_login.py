import xbot_visual
from .logger import logger

# 登录
def login(shop_name, target_url):
    page, code, message = xbot_visual.process.run(
        process="xbot_extensions.activity_5aa38261.main",
        package=__name__, inputs={
            "shopName": shop_name,
        },
        outputs=[
            "page",
            "code",
            "message",
        ], _block=("main", 1, "[通用]飞瓜登录"))
    
    url = page.get_url()
    logger.info(f"当前页面地址: {url}, 目标地址: {target_url}")
    if url != target_url:
        page.navigate(target_url)
        # 等待页面加载
        page.wait_load_completed()
    return page
